import { InvoiceImportItem, Prisma } from "@/prisma/generated";
import { AIModelId, llm } from "../ai/llm/llm";
import { braintrust } from "../tracing/braintrust";
import * as lfi from "lfi";
import { MotlFile } from "../core/motlFile";
import { prisma } from "@/prisma/prisma";
import { err, okAsync, ResultAsync, Result } from "neverthrow";
import { sleep } from "openai/core.mjs";
import yargs from "yargs";
import { cryptoCompareApi } from "../core/cryptoCompareApi";
import { invoiceAi } from "./invoiceAi";
import { json } from "../core/utils/jsonUtils";
import { cli } from "../core/utils/cliUtils";
import { createInvoice } from "./invoiceService";
import limitConcur from "limit-concur";
import { documentAI } from "../pdfParser/documentAi";

export async function parseInvoices(args: { invoiceImportItems: InvoiceImportItem[]; baseDetailsAiModel: AIModelId; invoiceValuesAiModel: AIModelId }) {
  const { invoiceImportItems } = args;

  const parseInvoicesRes = await lfi.pipe(
    lfi.asConcur(invoiceImportItems),
    // lfi.mapConcur((item) =>
    //   braintrust.trace("processInvoice", async (span) =>
    //     extractBaseDetails({ item, aiModel: args.baseDetailsAiModel }).then((res) =>
    //       res.map((baseDetailsRes) => {
    //         if (baseDetailsRes.llmRes.object.invoiceHasVatTAx) {
    //           return extractInvoiceValuesWithVAT({ item, aiModel: args.invoiceValuesAiModel, file: baseDetailsRes.file }).then((r) =>
    //             r.map((r) => ({ baseDetails: baseDetailsRes.llmRes, invoiceValues: r, item }))
    //           );
    //         } else {
    //           return extractInvoiceValuesWithoutVAT({ item, aiModel: args.invoiceValuesAiModel, file: baseDetailsRes.file }).then((r) =>
    //             r.map((r) => ({ baseDetails: baseDetailsRes.llmRes, invoiceValues: r, item }))
    //           );
    //         }
    //       })
    //     )
    //   )
    // ),
    lfi.mapConcur(
      limitConcur(20, async (item) => {
        return braintrust.trace("processInvoice", async (span) => {
          span.log({
            input: {
              item,
            },
          });
          const file = await MotlFile.parse(item.fileUrl);

          // const parsedText = await documentAI.parseDocument({ file });
          // if (parsedText.isErr()) {
          //   return err({
          //     type: "parseDocumentError",
          //     message: "error parsing document",
          //     err: parsedText.error,
          //   });
          // }

          // if (parsedText.value.parsedText.includes("Hutchison Drei"))
          //   return err({
          //     type: "shouldNotBeProcessed",
          //     reason: "Hutchison Drei",
          //     item,
          //   });

          const isInvoiceRes = await invoiceAi.detectInvoice({
            item,
            file,
            aiModel: args.baseDetailsAiModel,
          });

          if (isInvoiceRes.isErr()) {
            return err({
              type: "detectInvoiceError",
              message: (isInvoiceRes.error as any).message,
              err: isInvoiceRes.error,
            });
          }
          await prisma.invoiceImportItem.update({
            where: {
              id: item.id,
            },
            data: {
              // @ts-ignore
              isInvoice: isInvoiceRes.value.isInvoice,
              isImported: true,
            },
          });
          // @ts-ignore
          if (!isInvoiceRes.value.isInvoice) {
            return err({
              type: "notAnInvoice",
              message: "the document is not an invoice",
            });
          }

          return invoiceAi
            .parseInvoice({
              file,
              baseDetailsAiModel: args.baseDetailsAiModel,
              invoiceValuesAiModel: args.invoiceValuesAiModel,
            })
            .map((r) => ({
              ...r,
              item,
            }));
        });
      })
    ),
    // lfi.mapConcur((item) => extractInvoiceValues({ aiModel: args.invoiceValuesAiModel, item, file: item.fileUrl })),
    lfi.reduceConcur(lfi.toArray())
  );

  return parseInvoicesRes;
}

async function main() {
  const cliArgs = await yargs(process.argv.slice(2))
    .option("limit", { type: "number", default: 1 })
    .option("delete", { type: "boolean", default: false })
    .option("externalId", { type: "string", default: undefined })
    .parse();

  if (cliArgs.delete) {
    await prisma.invoice.deleteMany({});
    await prisma.vendor.deleteMany({});
  }

  // const logger = await braintrust.getLogger();

  const invoiceImportItems = await prisma.invoiceImportItem.findMany({
    where: {
      externalId: cliArgs.externalId,
      importItemType: "GMAIL_ATTACHMENT",
    },
    orderBy: {
      itemDate: "asc",
    },
    take: cliArgs.limit === -1 ? undefined : cliArgs.limit,
  });

  console.log("invoiceImportItems fetched:", invoiceImportItems.length);

  const invoiceResults = await parseInvoices({
    baseDetailsAiModel: "gpt-4.1",
    invoiceValuesAiModel: "o4-mini",
    invoiceImportItems: invoiceImportItems,
  });

  const successRes = invoiceResults.map((r) => r.unwrapOr(null)).filter((r) => r != null);

  const results = await lfi.pipe(
    lfi.asAsync(
      successRes.map((r) =>
        createInvoice({
          invoice: {
            date: new Date(r.baseDetails.object.invoiceDate),
            invoiceReference: r.baseDetails.object.invoiceReference,
            recipient: r.baseDetails.object.recipient as any,
            amountGross: r.invoiceValues.amountGross,
            amountNet: r.invoiceValues.amountNet,
            amountVat: r.invoiceValues.amountVat,
            hasVAT: r.invoiceValues.hasVAT,
            isReverseCharge: r.hasTaxRes.object.isReverseCharge,
            currencyCode: r.hasTaxRes.object.currencyCode,
            sourceAmountGross: "sourceAmountGross" in r.invoiceValues ? (r.invoiceValues.sourceAmountGross as number) : undefined,
            sourceAmountNet: "sourceAmountNet" in r.invoiceValues ? (r.invoiceValues.sourceAmountNet as number) : undefined,
            sourceAmountVat: "sourceAmountVat" in r.invoiceValues ? (r.invoiceValues.sourceAmountVat as number) : undefined,
            conversationRate: "conversationRate" in r.invoiceValues ? (r.invoiceValues.conversationRate as number) : undefined,
            lineItems: {
              createMany: {
                data: r.invoiceValues.items.map((item) => ({
                  name: item.name,
                  description: item.description,
                  priceGross: item.priceGross,
                  priceNet: item.priceNet,
                  vatRate: item.vatAmount,
                  vatAmount: item.vatAmount,
                })),
              },
            },
          },
          vendor: {
            name: r.baseDetails.object.vendor.companyName,
            street: r.baseDetails.object.vendor.address.street,
            houseNumber: r.baseDetails.object.vendor.address.houseNumber,
            city: r.baseDetails.object.vendor.address.city,
            country: r.baseDetails.object.vendor.address.country ?? "",
            vatNumber: r.baseDetails.object.vendor.uidNumber,
            contactPerson: r.baseDetails.object.vendor.contactPerson,
            email: r.baseDetails.object.vendor.email,
            phone: r.baseDetails.object.vendor.phone,
          },
          importItem: r.item,
        })
      )
    ),
    lfi.reduceAsync(lfi.toArray())
  );

  const awaitedRes = await Promise.all(results);

  const successFulDbWrites = awaitedRes.map((r) => r.unwrapOr(null)).filter((r) => r != null);

  console.log("successFulDbWrites", successFulDbWrites.length);

  Result.combine(awaitedRes).mapErr((failedDbWrites) => {
    console.log("failedDbWrites", failedDbWrites);
  });

  // console.log("result", json.prettyPrint(successRes.map((r) => r.object)));

  const failedResults = invoiceResults.filter((res) => res.isErr());
  if (failedResults.length > 0) console.log("Failed results:", json.prettyPrint(failedResults.map((r) => r)));

  const cachedResponses = successRes.filter((item) => item?.baseDetails);

  console.log(`total documents: ${invoiceResults.length}`);

  console.log(`cached responses: ${cachedResponses.length}/${successRes.length}`);
  console.error(`errors in invoice detection: ${failedResults.length}`);

  console.log("llm.totalUsage", llm.getUsageCost());

  // await prisma.invoiceImportItem.updateMany({
  //   where: {
  //     id: {
  //       in: results.map((r) => r.object.id),
  //     },
  //   },
  //   data: {
  //     isInvoice: true,
  //   },
  // });
}

cli.scafold({
  main,
});
